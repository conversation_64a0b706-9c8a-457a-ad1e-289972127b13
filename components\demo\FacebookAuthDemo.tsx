import React, { useState } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import * as Facebook from 'expo-auth-session/providers/facebook';
import { makeRedirectUri, ResponseType } from 'expo-auth-session';
import { FacebookAuthProvider, signInWithCredential } from 'firebase/auth';
import { auth } from '@/config/firebase';
import { useAppDispatch } from '@/store/hooks';
import { registerWithFacebook } from '@/store/slices/authSlice';
import ThemedButton from '@/components/common/ThemedButton';
import { ThemedText } from '@/components/common/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING } from '@/constants/theme';

/**
 * Demo component for testing Facebook authentication
 * This component can be used for testing and development purposes
 */
export default function FacebookAuthDemo() {
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState<string>('');
    const dispatch = useAppDispatch();

    // Configure Facebook auth request
    const [request, response, promptAsync] = Facebook.useAuthRequest({
        clientId: process.env.EXPO_PUBLIC_APP_ID || '',
        responseType: ResponseType.Token,
        scopes: ['public_profile', 'email'],
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: '/auth/facebook'
        }),
    });

    // Handle Facebook authentication response
    React.useEffect(() => {
        if (response?.type === 'success') {
            handleFacebookAuth(response);
        } else if (response?.type === 'error') {
            setLoading(false);
            setResult(`Error: ${response.error?.message || 'Facebook authentication failed'}`);
        } else if (response?.type === 'cancel') {
            setLoading(false);
            setResult('Authentication cancelled by user');
        }
    }, [response]);

    const handleFacebookAuth = async (authResponse: any) => {
        try {
            setLoading(true);
            setResult('Processing Facebook authentication...');

            if (!authResponse.authentication?.accessToken) {
                throw new Error('No access token received from Facebook');
            }

            // Create Firebase credential
            const credential = FacebookAuthProvider.credential(authResponse.authentication.accessToken);
            
            // Sign in to Firebase
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;

            setResult(`Firebase sign-in successful. UID: ${user.uid}`);

            // Register with backend
            const backendResult = await dispatch(registerWithFacebook(user.uid)).unwrap();
            
            if (backendResult.status) {
                setResult(`✅ Facebook authentication successful!\nUser: ${backendResult.user?.name || 'Unknown'}\nEmail: ${backendResult.user?.email || 'Unknown'}\nRequires Phone: ${backendResult.requiresPhone ? 'Yes' : 'No'}`);
            } else {
                setResult(`❌ Backend registration failed: ${backendResult.message || 'Unknown error'}`);
            }
        } catch (error: any) {
            console.error('Facebook auth demo error:', error);
            setResult(`❌ Error: ${error.message || 'Facebook authentication failed'}`);
        } finally {
            setLoading(false);
        }
    };

    const handleTestFacebookAuth = async () => {
        try {
            setLoading(true);
            setResult('Starting Facebook authentication...');

            if (!request) {
                throw new Error('Facebook authentication not ready. Please try again.');
            }

            await promptAsync();
        } catch (error: any) {
            setLoading(false);
            setResult(`❌ Error: ${error.message || 'Failed to start Facebook authentication'}`);
        }
    };

    const clearResult = () => {
        setResult('');
    };

    return (
        <View style={styles.container}>
            <ThemedText style={styles.title}>Facebook Auth Demo</ThemedText>
            
            <ThemedText style={styles.subtitle}>
                Test Facebook authentication integration
            </ThemedText>

            <View style={styles.buttonContainer}>
                <ThemedButton
                    style={[styles.button, styles.facebookButton]}
                    onPress={handleTestFacebookAuth}
                    disabled={loading}
                >
                    <View style={styles.buttonContent}>
                        <Ionicons 
                            name="logo-facebook" 
                            size={20} 
                            color={COLORS.white} 
                            style={styles.icon}
                        />
                        {loading ? (
                            <ActivityIndicator color={COLORS.white} style={styles.loader} />
                        ) : null}
                        <ThemedText style={styles.buttonText}>
                            {loading ? 'Authenticating...' : 'Test Facebook Login'}
                        </ThemedText>
                    </View>
                </ThemedButton>

                {result ? (
                    <ThemedButton
                        style={[styles.button, styles.clearButton]}
                        onPress={clearResult}
                    >
                        <ThemedText style={styles.clearButtonText}>Clear Result</ThemedText>
                    </ThemedButton>
                ) : null}
            </View>

            {result ? (
                <View style={styles.resultContainer}>
                    <ThemedText style={styles.resultTitle}>Result:</ThemedText>
                    <ThemedText style={styles.resultText}>{result}</ThemedText>
                </View>
            ) : null}

            <View style={styles.infoContainer}>
                <ThemedText style={styles.infoTitle}>Configuration:</ThemedText>
                <ThemedText style={styles.infoText}>
                    App ID: {process.env.EXPO_PUBLIC_APP_ID || 'Not configured'}
                </ThemedText>
                <ThemedText style={styles.infoText}>
                    Request Ready: {request ? '✅' : '❌'}
                </ThemedText>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: SPACING.lg,
        backgroundColor: COLORS.background,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: SPACING.xl,
        color: COLORS.text.secondary,
    },
    buttonContainer: {
        marginBottom: SPACING.xl,
    },
    button: {
        marginBottom: SPACING.md,
        paddingVertical: SPACING.md,
        borderRadius: 8,
    },
    facebookButton: {
        backgroundColor: '#1877F2',
    },
    clearButton: {
        backgroundColor: COLORS.error,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    icon: {
        marginRight: SPACING.sm,
    },
    loader: {
        marginRight: SPACING.sm,
    },
    buttonText: {
        color: COLORS.white,
        fontSize: 16,
        fontWeight: '600',
    },
    clearButtonText: {
        color: COLORS.white,
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    resultContainer: {
        backgroundColor: COLORS.surface.secondary,
        padding: SPACING.md,
        borderRadius: 8,
        marginBottom: SPACING.lg,
    },
    resultTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    resultText: {
        fontSize: 14,
        color: COLORS.text.secondary,
        lineHeight: 20,
    },
    infoContainer: {
        backgroundColor: COLORS.surface.primary,
        padding: SPACING.md,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: COLORS.border.secondary,
    },
    infoTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    infoText: {
        fontSize: 14,
        color: COLORS.text.secondary,
        marginBottom: SPACING.xs,
    },
});
