import { registerWithFacebook } from '@/store/slices/authSlice';

// Mock the environment variables
process.env.EXPO_PUBLIC_APP_ID = '1215086369859674';
process.env.EXPO_PUBLIC_API_BASE_URL = 'https://car.2ndcar.in/api';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock Redux store
const mockDispatch = jest.fn();
const mockGetState = jest.fn();

describe('Facebook Authentication', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should have Facebook App ID configured', () => {
    expect(process.env.EXPO_PUBLIC_APP_ID).toBe('1215086369859674');
  });

  test('registerWithFacebook should be defined', () => {
    expect(registerWithFacebook).toBeDefined();
    expect(typeof registerWithFacebook).toBe('function');
  });

  test('registerWithFacebook should create correct action type', () => {
    const uid = 'test-facebook-uid';
    const action = registerWithFacebook(uid);
    expect(action.type).toBe('auth/registerWithFacebook/pending');
  });

  test('should handle successful Facebook registration', async () => {
    const mockResponse = {
      status: true,
      user: { id: 1, name: 'Test User', email: '<EMAIL>' },
      authorisation: { token: 'test-token' },
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: jest.fn().mockResolvedValueOnce(mockResponse),
    });

    const thunk = registerWithFacebook('test-uid');
    const result = await thunk(mockDispatch, mockGetState, undefined);

    expect(result.payload).toEqual(mockResponse);
  });

  test('should handle Facebook registration requiring phone verification', async () => {
    const mockResponse = {
      status: false,
      user: { id: 1, name: 'Test User', email: '<EMAIL>', status: '0' },
      requiresPhone: true,
      uid: 'test-uid',
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: jest.fn().mockResolvedValueOnce(mockResponse),
    });

    const thunk = registerWithFacebook('test-uid');
    const result = await thunk(mockDispatch, mockGetState, undefined);

    expect(result.payload.requiresPhone).toBe(true);
    expect(result.payload.uid).toBe('test-uid');
  });

  test('should handle Facebook registration error', async () => {
    const mockError = new Error('Facebook registration failed');
    (global.fetch as jest.Mock).mockRejectedValueOnce(mockError);

    const thunk = registerWithFacebook('test-uid');
    
    try {
      await thunk(mockDispatch, mockGetState, undefined);
    } catch (error) {
      expect(error.message).toBe('Facebook registration failed');
    }
  });
});
