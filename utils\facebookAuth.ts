import * as WebBrowser from 'expo-web-browser';
import * as Facebook from 'expo-auth-session/providers/facebook';
import { useEffect, useState } from 'react';
import { auth } from '@/config/firebase';
import { FacebookAuthProvider, signInWithCredential, User } from 'firebase/auth';
import { useAppDispatch } from '@/store/hooks';
import { Platform } from 'react-native';
import { registerWithFacebook } from '@/store/slices/authSlice';

// Complete any in-progress auth session (important for web)
WebBrowser.maybeCompleteAuthSession();

// Facebook App ID from environment variables
const FACEBOOK_APP_ID = process.env.EXPO_PUBLIC_APP_ID;

if (!FACEBOOK_APP_ID) {
  throw new Error('Facebook App ID not configured. Please set EXPO_PUBLIC_APP_ID in your environment variables.');
}

export function useFacebookAuth() {
  const [userInfo, setUserInfo] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useAppDispatch();

  const [request, response, promptAsync] = Facebook.useAuthRequest({
    clientId: FACEBOOK_APP_ID,
    responseType: Facebook.ResponseType.Token,
    scopes: ['public_profile', 'email'],
    redirectUri: Platform.select({
      web: `${window.location.origin}/auth/facebook`,
      default: 'fb' + FACEBOOK_APP_ID + '://authorize',
    }),
  });

  useEffect(() => {
    if (response?.type === 'success') {
      handleSignInWithFacebook(response.authentication);
    } else if (response?.type === 'error') {
      setError(response.error?.message || 'Facebook authentication failed');
      setLoading(false);
    } else if (response?.type === 'cancel') {
      setLoading(false);
    }
  }, [response]);

  const handleSignInWithFacebook = async (authentication: any) => {
    try {
      setLoading(true);
      setError(null);

      if (!authentication?.accessToken) {
        throw new Error('No access token received from Facebook');
      }

      // Create Firebase credential with Facebook access token
      const credential = FacebookAuthProvider.credential(authentication.accessToken);
      
      // Sign in to Firebase
      const userCredential = await signInWithCredential(auth, credential);
      const user = userCredential.user;

      console.log('Facebook Firebase sign-in successful, UID:', user.uid);

      // Register with backend using Redux action
      const result = await dispatch(registerWithFacebook(user.uid)).unwrap();
      
      if (result.status) {
        setUserInfo(user);
        console.log('Facebook authentication completed successfully');
        return { success: true, user, requiresPhone: result.requiresPhone };
      } else {
        throw new Error(result.message || 'Facebook registration failed');
      }
    } catch (error: any) {
      console.error('Facebook authentication error:', error);
      setError(error.message || 'Facebook authentication failed');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const signInWithFacebook = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!request) {
        throw new Error('Facebook authentication not ready. Please try again.');
      }

      const result = await promptAsync();
      
      if (result.type === 'success') {
        return await handleSignInWithFacebook(result.authentication);
      } else if (result.type === 'cancel') {
        console.log('Facebook sign-in cancelled by user');
        return { success: false, cancelled: true };
      } else {
        throw new Error('Facebook authentication failed');
      }
    } catch (error: any) {
      console.error('Facebook sign-in error:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  return {
    userInfo,
    loading,
    error,
    signInWithFacebook,
    request,
  };
}

// Standalone function for direct use
export const facebookSignIn = async () => {
  try {
    if (!FACEBOOK_APP_ID) {
      throw new Error('Facebook App ID not configured');
    }

    const request = Facebook.useAuthRequest({
      clientId: FACEBOOK_APP_ID,
      responseType: Facebook.ResponseType.Token,
      scopes: ['public_profile', 'email'],
      redirectUri: Platform.select({
        web: `${window.location.origin}/auth/facebook`,
        default: 'fb' + FACEBOOK_APP_ID + '://authorize',
      }),
    });

    const result = await request[2](); // promptAsync is the third element

    if (result.type === 'success' && result.authentication?.accessToken) {
      const credential = FacebookAuthProvider.credential(result.authentication.accessToken);
      const userCredential = await signInWithCredential(auth, credential);
      return { success: true, user: userCredential.user };
    }

    return { success: false, error: 'Facebook authentication failed' };
  } catch (error: any) {
    console.error('Facebook sign-in error:', error);
    return { success: false, error: error.message };
  }
};
